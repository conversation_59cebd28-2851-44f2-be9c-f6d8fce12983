from enum import Enum

class FinancialTypes(Enum):
    """
    Enum representing subcategories under the Financial & Investment classification.
    """

    BANKING_AND_ACCOUNTS = "Banking & Accounts"
    LOANS_AND_CREDIT = "Loans & Credit"
    INVESTMENTS_AND_RETIREMENT = "Investments & Retirement"
    INCOME_AND_BENEFITS = "Income & Benefits"
    BILLING_INVOICES_PAYMENTS = "Billing, Invoices & Payments"
    PROPERTY_AND_ASSET_MANAGEMENT = "Property & Asset Management"


class BankingAndAccounts(Enum):
    ACCOUNT_BALANCE = "Account Balance"
    ACCOUNT_STATEMENT = "Account Statement"
    BANK_ACCOUNT_CLOSURE_CONFIRMATION = "Bank Account Closure Confirmation"
    BANK_DEPOSIT_HOLD_NOTICE = "Bank Deposit Hold Notice"
    BANK_LETTER = "Bank Letter"
    BANK_STATEMENTS = "Bank Statements"
    BALANCE_VERIFICATION = "Balance Verification"
    DEPOSIT_RECEIPT = "Deposit Receipt"
    DIRECT_DEPOSIT_FORM = "Direct Deposit Form"
    DIRECT_DEPOSIT_LETTER = "Direct Deposit Letter"
    DIRECT_DEPOSIT_REQUEST_FORM = "Direct Deposit Request Form"
    FINANCIAL_ACCOUNT_CHANGE_NOTIFICATION = "Financial Account Change Notification"
    LEDGER = "Ledger"
    PAPERLESS_STATEMENT_ENROLLMENT = "Paperless Statement Enrollment Confirmation"
    RECONCILIATION_REPORT = "Reconciliation Report"
    TRUE_LINK_CARD = "True Link Card"
    TRUE_LINK_CARD_LETTER = "True Link Card Letter"
    TRUST_ACCOUNT_RECONCILIATION = "Trust Account Reconciliation"


class LoansAndCredit(Enum):
    """Enum representing various loans and credit-related document types."""

    ACF_APPLICATION = "ACF Application"
    ACF_PAYOFF = "ACF Payoff"
    AUTO_LOAN_STATEMENT = "Auto Loan Statement"
    CREDIT_CARD = "Credit Card"
    CREDIT_CARD_STATEMENTS = "Credit Card Statements"
    CREDIT_DISPUTE_FORM = "Credit Dispute Form"
    DEBT_COLLECTOR_LETTER = "Debt Collector Letter"
    LOAN = "Loan"
    LOAN_DOCUMENTS = "Loan Documents"
    MORTGAGE = "Mortgage"
    MORTGAGE_LOAN_STATEMENT = "Mortgage Loan Statement"
    MORTGAGE_PAY_OFF = "Mortgage Pay off"
    MORTGAGE_STATEMENTS = "Mortgage Statements"


class InvestmentsAndRetirement(Enum):
    """Enum representing various investments and retirement-related document types."""

    ANNUITY_CERTIFICATE = "Annuity Certificate"
    ANNUITY_PAYMENT_NOTIFICATION = "Annuity Payment Notification"
    ANNUITY_STATEMENT = "Annuity Statement"
    IRA_STATEMENTS = "IRA Statements"
    LETTER_RE_RETIREMENT = "Letter re Retirement"
    QUARTERLY_RETIREMENT_SAVINGS_PORTFOLIO_STATEMENT = "Quarterly Retirement Savings Portfolio Statement"
    RETIREMENT_ACCOUNT_STATEMENT = "Retirement Account Statement"
    RETIREMENT_AFFIDAVIT = "Retirement Affidavit"
    RETIREMENT_DOCUMENTS = "Retirement Documents"
    FINANCIAL_SUITABILITY_REVIEW_LETTER = "Financial Suitability Review Letter"
    TRANSAMERICA_STATEMENT = "Transamerica Statement"
    FINANCIAL_ASSISTANCE_APPLICATION = "Financial Assistance Application" # new


class IncomeAndBenefitsFinancial(Enum):
    """Enum representing income and financial benefit-related document types."""

    ANNUAL_SSA_BENEFIT_STATEMENT = "Annual SSA Benefit Statement"
    EMPLOYER_WAGES_AFFIDAVIT = "Employer Wages Affidavit"
    INCOME_VERIFICATION = "Income Verification"
    LOST_WAGE_DOCUMENTS = "Lost Wage Documents"
    NEW_SSA_BENEFIT_AMOUNT_NOTIFICATION = "New SSA Benefit Amount Notification"
    PAYSTUBS = "Paystubs"
    STATEMENT_OF_ASSISTANCE = "Statement of Assistance"
    W2 = "W2"
    YEAR_END_TAX_DOCUMENTS = "Year-end Tax Documents"
    FMLA_LETTER = "FMLA Letter" # new


class BillingInvoicesPayments(Enum):
    """Enum representing general billing, invoice, and payment-related document types."""

    LEDGER = "Ledger"
    BILLING_RECORD = "Billing Record"
    CABLE_TV_SERVICE_INVOICE = "Cable/TV Service Invoice"
    CHECK = "Check"
    CO_PAYMENT_RECORD = "Co-Payment Record"
    COST_ESTIMATE = "Cost Estimate"
    FEE_LETTER = "Fee Letter"
    INVOICE = "Invoice"
    ITEMIZED_STATEMENT = "Itemized Statement"
    NO_PAYMENT_CONFIRMATION = "No Payment Confirmation"
    NOTICE_OF_DENIAL_OF_PAYMENT = "Notice of Denial of Payment"
    PAST_DUE_BALANCE_NOTICE = "Past Due Balance Notice"
    PAYMENT_CONFIRMATION = "Payment Confirmation"
    PAYMENT_LOG = "Payment Log"
    PAYMENT_RECORD = "Payment Record"
    PHONE_BILL = "Phone Bill"
    PRICE_INCREASE_LETTER = "Price Increase Letter"
    PROPERTY_DAMAGE_PAYMENT_RECEIPT = "Property Damage Payment Receipt"
    REIMBURSEMENT_LETTER = "Reimbursement Letter"
    REIMBURSEMENT_RECORD = "Reimbursement Record"
    REIMBURSEMENT_REQUEST = "Reimbursement Request"
    RENTAL_REIMBURSEMENT = "Rental Reimbursement"
    SETTLEMENT_CHECK = "Settlement Check"
    UPS_INVOICE = "UPS Invoice"
    UTILITY_BILL = "Utility Bill"
    UTILITY_LETTER = "Utility Letter"
    UTILITY_NOTICE = "Utility Notice"
    VEHICLE_RENEWAL_FEE_RECEIPT = "Vehicle Renewal Fee Receipt"
    PAYMENT_RECIPIENT = "Payment Recipient"


class PropertyAndAssetManagement(Enum):
    """Enum representing property and asset management-related document types."""

    HOA_BALANCE = "HOA Balance"
    HOA_BUDGET = "HOA Budget"
    LEASE_DOCUMENT = "Lease Document"
    MOVE_IN_STATEMENT = "Move in Statement"
    MORTGAGE = "Mortgage"
    MORTGAGE_LOAN_STATEMENT = "Mortgage Loan Statement"
    MORTGAGE_PAY_OFF = "Mortgage Pay off"
    MORTGAGE_STATEMENTS = "Mortgage Statements"
    NOTICE_OF_ASSESSMENT = "Notice of Assessment"
    PROPERTY_DEED = "Property Deed"
    PROPERTY_TAX_ASSESSMENT = "Property Tax Assessment / Condo Valuation Report"
