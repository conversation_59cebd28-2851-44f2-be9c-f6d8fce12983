import json
from bs4 import BeautifulSoup
import re # Import re for cleaning whitespace

def extract_plain_text_from_email_body(email_data):
    """
    Extracts plain text content from the email_body field of an email dictionary.

    Args:
        email_data (dict): The dictionary containing email information.

    Returns:
        str: The extracted plain text, or a message if extraction fails.
    """
    # First check if email_body is directly in email_data
    email_body = email_data.get("email_body")

    # If not found, check in metadata
    if not email_body:
        metadata = email_data.get("metadata", {})
        email_body = metadata.get("email_body")

    # Check if email_body exists and is a dictionary
    if not email_body or not isinstance(email_body, dict):
        return "Error: 'email_body' not found or not a dictionary."

    content = email_body.get("content")
    content_type = email_body.get("content_type")

    # Check if content exists and is a string
    if not content or not isinstance(content, str):
         # Return empty string if content is missing or not a string
        return ""

    if content_type == "html":
        try:
            # Use BeautifulSoup to parse the HTML content
            # We use "html.parser" which is a built-in parser
            soup = BeautifulSoup(content, "html.parser")

            # get_text() extracts all the text from the document,
            # typically ignoring script and style tags.
            # Using separator='\n' helps preserve some line breaks/paragraph separation
            text = soup.get_text(separator='\n')

            # Optional: Clean up excessive whitespace (like multiple newlines)
            # Replace multiple newlines with a maximum of two
            text = re.sub(r'\n\s*\n', '\n\n', text)
            # Remove leading/trailing whitespace from the entire string
            text = text.strip()
            # Remove leading/trailing whitespace from each line (optional, less aggressive)
            # text = '\n'.join(line.strip() for line in text.split('\n'))


            return text
        except Exception as e:
            # Handle potential parsing errors
            return f"Error parsing HTML: {e}"
    elif content_type == "plain":
        # If the content type is already plain, just return it
        # You might still want to strip leading/trailing whitespace
        return content.strip()
    else:
        # Handle other content types or cases where content_type is missing/unknown
        # You could return the raw content, an error, or an empty string
        return f"Unsupported content type: {content_type}. Returning raw"